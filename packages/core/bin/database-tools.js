#!/usr/bin/env node

/**
 * UNIVERSAL AI BRAIN - DATABASE MANAGEMENT TOOLS
 * 
 * This tool helps you manage and organize your Universal AI Brain database:
 * 
 *   npx @mongodb-ai/core setup-database     # Create perfect structure
 *   npx @mongodb-ai/core validate-database  # Check organization
 *   npx @mongodb-ai/core cleanup-database   # Fix chaos
 *   npx @mongodb-ai/core analyze-database   # Performance analysis
 * 
 * Ensures your MongoDB is perfectly organized for industry leadership!
 */

import { MongoClient } from 'mongodb';
import dotenv from 'dotenv';

dotenv.config();

console.log(`
🧠 UNIVERSAL AI BRAIN - DATABASE MANAGEMENT TOOLS
==================================================

These tools ensure your MongoDB database is perfectly organized
for industry-leading AI intelligence.

🎯 Available Commands:
   📊 setup-database    - Create perfect database structure
   🔍 validate-database - Check current organization
   🧹 cleanup-database  - Fix chaotic data organization
   📈 analyze-database  - Performance and usage analysis

🚀 Making your database the gold standard!

`);

class DatabaseManager {
  constructor() {
    this.connectionString = process.env.MONGODB_CONNECTION_STRING;
    this.databaseName = process.env.DATABASE_NAME || 'universal_ai_brain';
    
    if (!this.connectionString) {
      console.error('❌ MONGODB_CONNECTION_STRING environment variable required');
      process.exit(1);
    }
  }

  async setupDatabase() {
    console.log('🏗️  SETTING UP PERFECT DATABASE STRUCTURE');
    console.log('=========================================\n');

    const client = new MongoClient(this.connectionString);
    
    try {
      await client.connect();
      const db = client.db(this.databaseName);
      
      console.log(`📊 Connected to database: ${this.databaseName}`);
      
      // Create collections with perfect organization
      const collections = [
        { name: 'uab_memories', desc: 'Core semantic memories - The heart of AI intelligence' },
        { name: 'uab_sessions', desc: 'User sessions - Perfect conversation organization' },
        { name: 'uab_agents', desc: 'Agent registry - Framework integration metadata' },
        { name: 'uab_traces', desc: 'Execution traces - Production monitoring and debugging' },
        { name: 'uab_improvements', desc: 'Self-improvement tracking - AI that gets better' },
        { name: 'uab_metrics', desc: 'Performance analytics - Measure the 70% improvement' }
      ];

      console.log('📁 Creating collections with perfect organization...');
      for (const col of collections) {
        try {
          await db.createCollection(col.name);
          console.log(`✅ ${col.name} - ${col.desc}`);
        } catch (error) {
          if (error.code === 48) {
            console.log(`⚠️  ${col.name} already exists - skipping`);
          } else {
            throw error;
          }
        }
      }

      // Create production-optimized indexes
      console.log('\n🔍 Creating production-optimized indexes...');
      
      // Memories indexes (most critical)
      await this.createIndexSafely(db, 'uab_memories', { userId: 1, sessionId: 1, createdAt: -1 }, 'user_session_time');
      await this.createIndexSafely(db, 'uab_memories', { userId: 1, type: 1, importance: -1 }, 'user_type_importance');
      await this.createIndexSafely(db, 'uab_memories', { agentId: 1, createdAt: -1 }, 'agent_time');
      await this.createIndexSafely(db, 'uab_memories', { content: 'text' }, 'content_search');
      
      // Sessions indexes
      await this.createIndexSafely(db, 'uab_sessions', { userId: 1, lastActivity: -1 }, 'user_activity');
      await this.createIndexSafely(db, 'uab_sessions', { agentId: 1, status: 1 }, 'agent_status');
      
      // Traces indexes
      await this.createIndexSafely(db, 'uab_traces', { agentId: 1, startTime: -1 }, 'agent_time');
      await this.createIndexSafely(db, 'uab_traces', { sessionId: 1, operation: 1 }, 'session_operation');
      
      console.log('✅ All indexes created for optimal performance!');
      
      console.log('\n🎯 Vector Search Index Configuration:');
      console.log('   Collection: uab_memories');
      console.log('   Field: embedding (1536 dimensions)');
      console.log('   Similarity: cosine');
      console.log('   Filters: userId, type, framework');
      console.log('   ⚠️  Create this manually in MongoDB Atlas UI');

      console.log('\n✅ PERFECT DATABASE STRUCTURE CREATED!');
      console.log('🚀 Your Universal AI Brain is ready for industry leadership!');
      
    } catch (error) {
      console.error('❌ Database setup failed:', error);
      throw error;
    } finally {
      await client.close();
    }
  }

  async validateDatabase() {
    console.log('🔍 VALIDATING DATABASE ORGANIZATION');
    console.log('===================================\n');

    const client = new MongoClient(this.connectionString);
    
    try {
      await client.connect();
      const db = client.db(this.databaseName);
      
      console.log(`📊 Analyzing database: ${this.databaseName}`);
      
      // Check collections
      const collections = await db.listCollections().toArray();
      const collectionNames = collections.map(c => c.name);
      
      console.log('\n📁 Collection Analysis:');
      console.log('----------------------');
      
      const requiredCollections = [
        'uab_memories', 'uab_sessions', 'uab_agents', 
        'uab_traces', 'uab_improvements', 'uab_metrics'
      ];
      
      let score = 0;
      const maxScore = requiredCollections.length;
      
      for (const required of requiredCollections) {
        if (collectionNames.includes(required)) {
          console.log(`✅ ${required} - Perfect organization`);
          score++;
        } else {
          console.log(`❌ ${required} - Missing! Run setup-database`);
        }
      }
      
      // Check for chaotic collections
      const chaoticCollections = collectionNames.filter(name => 
        !name.startsWith('uab_') && 
        !name.startsWith('system.') &&
        name !== 'uab_vector_search_config'
      );
      
      if (chaoticCollections.length > 0) {
        console.log('\n⚠️  CHAOTIC COLLECTIONS DETECTED:');
        chaoticCollections.forEach(name => {
          console.log(`   - ${name} (not following Universal AI Brain naming)`);
        });
        console.log('   Run cleanup-database to organize these');
      }
      
      // Check indexes
      console.log('\n🔍 Index Analysis:');
      console.log('------------------');
      
      if (collectionNames.includes('uab_memories')) {
        const indexes = await db.collection('uab_memories').listIndexes().toArray();
        const indexNames = indexes.map(i => i.name);
        
        const criticalIndexes = ['user_session_time', 'user_type_importance', 'content_search'];
        let indexScore = 0;
        
        for (const critical of criticalIndexes) {
          if (indexNames.includes(critical)) {
            console.log(`✅ ${critical} - Optimized for performance`);
            indexScore++;
          } else {
            console.log(`❌ ${critical} - Missing! Run setup-database`);
          }
        }
        
        console.log(`📊 Index Score: ${indexScore}/${criticalIndexes.length}`);
      }
      
      // Overall assessment
      console.log('\n🏆 OVERALL ASSESSMENT:');
      console.log('======================');
      
      const organizationScore = (score / maxScore) * 100;
      
      if (organizationScore === 100 && chaoticCollections.length === 0) {
        console.log('✅ PERFECT ORGANIZATION!');
        console.log('🚀 Your database meets industry-leading standards!');
        console.log('🌟 Ready for Universal AI Brain deployment!');
      } else if (organizationScore >= 80) {
        console.log('⚠️  GOOD ORGANIZATION');
        console.log(`📊 Score: ${organizationScore.toFixed(1)}%`);
        console.log('🔧 Minor improvements needed - run setup-database');
      } else {
        console.log('❌ NEEDS MAJOR REORGANIZATION');
        console.log(`📊 Score: ${organizationScore.toFixed(1)}%`);
        console.log('🧹 Run cleanup-database and setup-database');
      }
      
    } catch (error) {
      console.error('❌ Database validation failed:', error);
      throw error;
    } finally {
      await client.close();
    }
  }

  async cleanupDatabase() {
    console.log('🧹 CLEANING UP CHAOTIC DATABASE ORGANIZATION');
    console.log('============================================\n');

    const client = new MongoClient(this.connectionString);
    
    try {
      await client.connect();
      const db = client.db(this.databaseName);
      
      console.log(`📊 Analyzing database: ${this.databaseName}`);
      
      // Find chaotic collections
      const collections = await db.listCollections().toArray();
      const chaoticCollections = collections.filter(col => 
        !col.name.startsWith('uab_') && 
        !col.name.startsWith('system.') &&
        col.name !== 'uab_vector_search_config'
      );
      
      if (chaoticCollections.length === 0) {
        console.log('✅ No chaotic collections found!');
        console.log('🌟 Your database is already perfectly organized!');
        return;
      }
      
      console.log('\n🔍 Chaotic collections detected:');
      chaoticCollections.forEach(col => {
        console.log(`   - ${col.name}`);
      });
      
      console.log('\n🧹 Cleanup recommendations:');
      console.log('---------------------------');
      
      for (const col of chaoticCollections) {
        const count = await db.collection(col.name).countDocuments();
        console.log(`📁 ${col.name} (${count} documents)`);
        
        // Suggest migration based on collection name patterns
        if (col.name.includes('memor') || col.name.includes('conversation')) {
          console.log(`   → Migrate to: uab_memories`);
        } else if (col.name.includes('session') || col.name.includes('chat')) {
          console.log(`   → Migrate to: uab_sessions`);
        } else if (col.name.includes('agent') || col.name.includes('bot')) {
          console.log(`   → Migrate to: uab_agents`);
        } else if (col.name.includes('trace') || col.name.includes('log')) {
          console.log(`   → Migrate to: uab_traces`);
        } else {
          console.log(`   → Review manually for migration`);
        }
      }
      
      console.log('\n⚠️  MANUAL MIGRATION REQUIRED');
      console.log('Data migration requires careful review to prevent data loss.');
      console.log('Please review the recommendations above and migrate data manually.');
      console.log('\nAfter migration, run: npx @mongodb-ai/core setup-database');
      
    } catch (error) {
      console.error('❌ Database cleanup analysis failed:', error);
      throw error;
    } finally {
      await client.close();
    }
  }

  async analyzeDatabase() {
    console.log('📈 DATABASE PERFORMANCE ANALYSIS');
    console.log('================================\n');

    const client = new MongoClient(this.connectionString);
    
    try {
      await client.connect();
      const db = client.db(this.databaseName);
      
      console.log(`📊 Analyzing database: ${this.databaseName}`);
      
      // Collection statistics
      const collections = ['uab_memories', 'uab_sessions', 'uab_agents', 'uab_traces'];
      
      console.log('\n📊 Collection Statistics:');
      console.log('-------------------------');
      
      for (const collName of collections) {
        try {
          const stats = await db.collection(collName).stats();
          const count = await db.collection(collName).countDocuments();
          
          console.log(`📁 ${collName}:`);
          console.log(`   Documents: ${count.toLocaleString()}`);
          console.log(`   Size: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);
          console.log(`   Indexes: ${stats.nindexes}`);
          console.log('');
        } catch (error) {
          console.log(`📁 ${collName}: Collection not found`);
        }
      }
      
      console.log('✅ Performance analysis complete!');
      
    } catch (error) {
      console.error('❌ Database analysis failed:', error);
      throw error;
    } finally {
      await client.close();
    }
  }

  async createIndexSafely(db, collectionName, indexSpec, indexName) {
    try {
      await db.collection(collectionName).createIndex(indexSpec, { name: indexName, background: true });
      console.log(`   ✅ ${indexName}: ${JSON.stringify(indexSpec)}`);
    } catch (error) {
      if (error.code === 85) {
        console.log(`   ⚠️  ${indexName}: Already exists`);
      } else {
        throw error;
      }
    }
  }
}

// Main execution
async function main() {
  const command = process.argv[2];
  const manager = new DatabaseManager();
  
  try {
    switch (command) {
      case 'setup-database':
        await manager.setupDatabase();
        break;
      case 'validate-database':
        await manager.validateDatabase();
        break;
      case 'cleanup-database':
        await manager.cleanupDatabase();
        break;
      case 'analyze-database':
        await manager.analyzeDatabase();
        break;
      default:
        console.log('❌ Unknown command. Available commands:');
        console.log('   setup-database    - Create perfect structure');
        console.log('   validate-database - Check organization');
        console.log('   cleanup-database  - Fix chaotic data');
        console.log('   analyze-database  - Performance analysis');
        process.exit(1);
    }
    
    console.log('\n🎉 Database management complete!');
    console.log('🧠 Universal AI Brain database is industry-ready!');
    
  } catch (error) {
    console.error('❌ Operation failed:', error.message);
    process.exit(1);
  }
}

main();
