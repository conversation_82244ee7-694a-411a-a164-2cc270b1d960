/**
 * @file VoyageAIEmbeddingProvider - Production-ready Voyage AI embedding implementation
 * 
 * This provides state-of-the-art Voyage AI embedding generation for the Universal AI Brain.
 * Voyage AI embeddings are superior to OpenAI for retrieval tasks and are optimized
 * for semantic search and RAG applications.
 * 
 * Features:
 * - State-of-the-art retrieval accuracy (better than OpenAI)
 * - Support for query vs document input types
 * - 32,000 token context length
 * - Flexible output dimensions
 * - Production-ready error handling and retry logic
 */

import { EmbeddingProvider } from '../vector/MongoVectorStore';

export interface VoyageAIConfig {
  apiKey: string;
  model: string;
  baseUrl?: string;
  maxRetries?: number;
  timeout?: number;
  batchSize?: number;
  outputDimension?: number;
}

export interface VoyageEmbeddingResponse {
  object: string;
  data: Array<{
    object: string;
    embedding: number[];
    index: number;
  }>;
  model: string;
  usage: {
    total_tokens: number;
  };
}

/**
 * VoyageAIEmbeddingProvider - State-of-the-art embedding implementation
 * 
 * Voyage AI provides superior embedding quality compared to OpenAI, especially
 * for retrieval and RAG applications. This provider implements the full
 * Voyage AI API with production-ready features.
 */
export class VoyageAIEmbeddingProvider implements EmbeddingProvider {
  private config: Required<VoyageAIConfig>;
  private requestCount: number = 0;
  private totalTokens: number = 0;

  constructor(config: VoyageAIConfig) {
    this.config = {
      maxRetries: 3,
      timeout: 30000,
      batchSize: 128, // Voyage AI supports larger batches
      baseUrl: 'https://api.voyageai.com/v1',
      outputDimension: 1024, // Default for voyage-3.5
      ...config
    };

    this.validateConfig();
  }

  /**
   * Generate embedding for a single text
   */
  async generateEmbedding(text: string, inputType: 'query' | 'document' = 'document'): Promise<number[]> {
    if (!text || text.trim().length === 0) {
      throw new Error('Text cannot be empty');
    }

    try {
      const embeddings = await this.generateEmbeddings([text], inputType);
      return embeddings[0];
    } catch (error) {
      console.error('Error generating Voyage AI embedding:', error);
      throw new Error(`Failed to generate Voyage AI embedding: ${error.message}`);
    }
  }

  /**
   * Generate embeddings for multiple texts (batch processing)
   */
  async generateEmbeddings(texts: string[], inputType: 'query' | 'document' = 'document'): Promise<number[][]> {
    if (!texts || texts.length === 0) {
      throw new Error('Texts array cannot be empty');
    }

    // Filter out empty texts
    const validTexts = texts.filter(text => text && text.trim().length > 0);
    if (validTexts.length === 0) {
      throw new Error('No valid texts provided');
    }

    try {
      // Process in batches if needed
      if (validTexts.length > this.config.batchSize) {
        return this.processBatches(validTexts, inputType);
      }

      return this.callVoyageAPI(validTexts, inputType);
    } catch (error) {
      console.error('Error generating Voyage AI embeddings:', error);
      throw new Error(`Failed to generate Voyage AI embeddings: ${error.message}`);
    }
  }

  /**
   * Get embedding dimensions for the current model
   */
  getDimensions(): number {
    const dimensionMap: Record<string, number> = {
      'voyage-3-large': 1024,
      'voyage-3.5': 1024,
      'voyage-3.5-lite': 1024,
      'voyage-code-3': 1024,
      'voyage-finance-2': 1024,
      'voyage-law-2': 1024,
      'voyage-multilingual-2': 1024,
      'voyage-large-2': 1536,
      'voyage-2': 1024,
      'voyage-code-2': 1536
    };

    return this.config.outputDimension || dimensionMap[this.config.model] || 1024;
  }

  /**
   * Get the current model name
   */
  getModel(): string {
    return this.config.model;
  }

  /**
   * Get usage statistics
   */
  getUsageStats(): { requestCount: number; totalTokens: number; estimatedCost: number } {
    // Voyage AI pricing (as of 2024)
    const costPerToken = this.getCostPerToken();
    const estimatedCost = this.totalTokens * costPerToken;

    return {
      requestCount: this.requestCount,
      totalTokens: this.totalTokens,
      estimatedCost
    };
  }

  /**
   * Reset usage statistics
   */
  resetUsageStats(): void {
    this.requestCount = 0;
    this.totalTokens = 0;
  }

  // Private methods

  private async processBatches(texts: string[], inputType: 'query' | 'document'): Promise<number[][]> {
    const results: number[][] = [];
    
    for (let i = 0; i < texts.length; i += this.config.batchSize) {
      const batch = texts.slice(i, i + this.config.batchSize);
      const batchResults = await this.callVoyageAPI(batch, inputType);
      results.push(...batchResults);
    }

    return results;
  }

  private async callVoyageAPI(texts: string[], inputType: 'query' | 'document'): Promise<number[][]> {
    const url = `${this.config.baseUrl}/embeddings`;
    
    const requestBody: any = {
      input: texts,
      model: this.config.model,
      input_type: inputType
    };

    // Add output dimension if supported by model
    if (this.supportsFlexibleDimensions()) {
      requestBody.output_dimension = this.config.outputDimension;
    }

    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= this.config.maxRetries; attempt++) {
      try {
        const response = await this.makeRequest(url, requestBody);
        
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(`Voyage AI API request failed: ${response.status} ${response.statusText} - ${JSON.stringify(errorData)}`);
        }

        const data: VoyageEmbeddingResponse = await response.json();
        
        // Update usage statistics
        this.requestCount++;
        this.totalTokens += data.usage.total_tokens;

        // Extract embeddings in the correct order
        const embeddings = data.data
          .sort((a, b) => a.index - b.index)
          .map(item => item.embedding);

        return embeddings;
      } catch (error) {
        lastError = error as Error;
        
        if (attempt < this.config.maxRetries) {
          const delay = Math.pow(2, attempt) * 1000; // Exponential backoff
          console.warn(`Voyage AI API attempt ${attempt + 1} failed, retrying in ${delay}ms:`, error.message);
          await this.sleep(delay);
        }
      }
    }

    throw lastError || new Error('All Voyage AI API attempts failed');
  }

  private async makeRequest(url: string, body: any): Promise<Response> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.config.apiKey}`
    };

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(body),
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private validateConfig(): void {
    if (!this.config.apiKey) {
      throw new Error('Voyage AI API key is required');
    }

    if (!this.config.model) {
      throw new Error('Voyage AI model is required');
    }

    const supportedModels = [
      'voyage-3-large',
      'voyage-3.5',
      'voyage-3.5-lite',
      'voyage-code-3',
      'voyage-finance-2',
      'voyage-law-2',
      'voyage-multilingual-2'
    ];

    if (!supportedModels.some(model => this.config.model.startsWith(model))) {
      console.warn(`Model ${this.config.model} may not be supported. Recommended models: ${supportedModels.join(', ')}`);
    }
  }

  private supportsFlexibleDimensions(): boolean {
    const flexibleModels = ['voyage-3-large', 'voyage-3.5', 'voyage-3.5-lite', 'voyage-code-3'];
    return flexibleModels.some(model => this.config.model.startsWith(model));
  }

  private getCostPerToken(): number {
    // Voyage AI pricing per token in USD (as of 2024)
    const costMap: Record<string, number> = {
      'voyage-3-large': 0.00000012,
      'voyage-3.5': 0.00000008,
      'voyage-3.5-lite': 0.00000004,
      'voyage-code-3': 0.00000008,
      'voyage-finance-2': 0.00000008,
      'voyage-law-2': 0.00000008,
      'voyage-multilingual-2': 0.00000008
    };

    return costMap[this.config.model] || 0.00000008;
  }

  /**
   * Test the embedding provider with a simple query
   */
  async test(): Promise<{ success: boolean; details: any }> {
    try {
      const testText = 'This is a test embedding for Universal AI Brain';
      const embedding = await this.generateEmbedding(testText, 'document');
      
      return {
        success: true,
        details: {
          provider: 'Voyage AI',
          model: this.config.model,
          dimensions: embedding.length,
          expectedDimensions: this.getDimensions(),
          sampleEmbedding: embedding.slice(0, 5), // First 5 values
          usageStats: this.getUsageStats(),
          advantages: [
            'State-of-the-art retrieval accuracy',
            'Better than OpenAI for semantic search',
            '32,000 token context length',
            'Optimized for RAG applications'
          ]
        }
      };
    } catch (error) {
      return {
        success: false,
        details: { error: error.message }
      };
    }
  }

  /**
   * Create a provider instance from environment variables
   */
  static fromEnv(): VoyageAIEmbeddingProvider {
    const apiKey = process.env.VOYAGE_API_KEY;
    const model = process.env.VOYAGE_MODEL || 'voyage-3.5';
    const outputDimension = process.env.VOYAGE_OUTPUT_DIMENSION ? 
      parseInt(process.env.VOYAGE_OUTPUT_DIMENSION) : undefined;

    if (!apiKey) {
      throw new Error('VOYAGE_API_KEY environment variable is required');
    }

    return new VoyageAIEmbeddingProvider({
      apiKey,
      model,
      outputDimension
    });
  }

  /**
   * Create a recommended provider for Universal AI Brain
   */
  static forUniversalAIBrain(apiKey: string): VoyageAIEmbeddingProvider {
    return new VoyageAIEmbeddingProvider({
      apiKey,
      model: 'voyage-3.5', // Best balance of quality and cost
      outputDimension: 1024,
      batchSize: 128,
      maxRetries: 3
    });
  }
}
